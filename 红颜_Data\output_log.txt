Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.047 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\BaiduNetdiskDownload\beauty\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 2104 -> priority: 1 
  Thread -> id: 11fc -> priority: 1 
  Thread -> id: 5f90 -> priority: 1 
  Thread -> id: 36a0 -> priority: 1 
UnloadTime: 2.127900 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.466900 ms (FindLiveObjects: 0.017300 ms CreateObjectMapping: 0.009700 ms MarkObjects: 0.428400 ms  DeleteObjects: 0.011100 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.666900 ms (FindLiveObjects: 0.016000 ms CreateObjectMapping: 0.012100 ms MarkObjects: 0.623600 ms  DeleteObjects: 0.014800 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

重复key:服药,xml=storysPY.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

The referenced script on this Behaviour is missing!
 
(Filename:  Line: 1649)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.598500 ms

Unloading 52 unused Assets to reduce memory usage. Loaded Objects now: 887.
Total: 36.111103 ms (FindLiveObjects: 0.024800 ms CreateObjectMapping: 0.015100 ms MarkObjects: 36.036701 ms  DeleteObjects: 0.034000 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 885.
Total: 36.839897 ms (FindLiveObjects: 0.029500 ms CreateObjectMapping: 0.016200 ms MarkObjects: 36.778400 ms  DeleteObjects: 0.015100 ms)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.859200 ms

Unloading 29 unused Assets to reduce memory usage. Loaded Objects now: 554.
Total: 34.367100 ms (FindLiveObjects: 0.069100 ms CreateObjectMapping: 0.012900 ms MarkObjects: 34.257401 ms  DeleteObjects: 0.026400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 556.
Total: 41.108101 ms (FindLiveObjects: 0.018500 ms CreateObjectMapping: 0.014700 ms MarkObjects: 41.050602 ms  DeleteObjects: 0.023600 ms)

The file 'D:/BaiduNetdiskDownload/beauty/红颜_Data/level2' is corrupted! Remove it and launch unity again!
[Position out of bounds!]
 
(Filename:  Line: 241)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.486100 ms

Unloading 6 unused Assets to reduce memory usage. Loaded Objects now: 3533.
Total: 38.927200 ms (FindLiveObjects: 0.097900 ms CreateObjectMapping: 0.066100 ms MarkObjects: 38.640800 ms  DeleteObjects: 0.121900 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3615.
Total: 38.237900 ms (FindLiveObjects: 0.096300 ms CreateObjectMapping: 0.045100 ms MarkObjects: 38.091999 ms  DeleteObjects: 0.003900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 1 unused Assets to reduce memory usage. Loaded Objects now: 3541.
Total: 39.283302 ms (FindLiveObjects: 0.105000 ms CreateObjectMapping: 0.057700 ms MarkObjects: 39.060001 ms  DeleteObjects: 0.060100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 10 unused Assets to reduce memory usage. Loaded Objects now: 3748.
Total: 38.550102 ms (FindLiveObjects: 0.106800 ms CreateObjectMapping: 0.055000 ms MarkObjects: 38.362598 ms  DeleteObjects: 0.025200 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3777.
Total: 37.019199 ms (FindLiveObjects: 0.127800 ms CreateObjectMapping: 0.052400 ms MarkObjects: 36.833500 ms  DeleteObjects: 0.005100 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3583.
Total: 37.897099 ms (FindLiveObjects: 0.124900 ms CreateObjectMapping: 0.045500 ms MarkObjects: 37.722000 ms  DeleteObjects: 0.003900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3778.
Total: 39.561802 ms (FindLiveObjects: 0.224400 ms CreateObjectMapping: 0.072800 ms MarkObjects: 39.259502 ms  DeleteObjects: 0.004000 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3583.
Total: 38.477398 ms (FindLiveObjects: 0.116300 ms CreateObjectMapping: 0.048700 ms MarkObjects: 38.308201 ms  DeleteObjects: 0.003700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3583.
Total: 37.368500 ms (FindLiveObjects: 0.114000 ms CreateObjectMapping: 0.049500 ms MarkObjects: 37.201199 ms  DeleteObjects: 0.003300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3589.
Total: 37.385799 ms (FindLiveObjects: 0.104400 ms CreateObjectMapping: 0.047700 ms MarkObjects: 37.229603 ms  DeleteObjects: 0.003600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 1 unused Assets to reduce memory usage. Loaded Objects now: 3589.
Total: 38.362499 ms (FindLiveObjects: 0.114300 ms CreateObjectMapping: 0.061000 ms MarkObjects: 37.989697 ms  DeleteObjects: 0.196700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 1 unused Assets to reduce memory usage. Loaded Objects now: 3589.
Total: 40.698700 ms (FindLiveObjects: 0.105600 ms CreateObjectMapping: 0.048000 ms MarkObjects: 40.478401 ms  DeleteObjects: 0.066100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3589.
Total: 38.324501 ms (FindLiveObjects: 0.120300 ms CreateObjectMapping: 0.066900 ms MarkObjects: 38.132702 ms  DeleteObjects: 0.004000 ms)

调用了未定义的story:打个广告先2
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 3 unused Assets to reduce memory usage. Loaded Objects now: 3792.
Total: 39.131702 ms (FindLiveObjects: 0.108300 ms CreateObjectMapping: 0.048700 ms MarkObjects: 38.797100 ms  DeleteObjects: 0.177000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5222.
Total: 37.607101 ms (FindLiveObjects: 0.164000 ms CreateObjectMapping: 0.103400 ms MarkObjects: 37.333202 ms  DeleteObjects: 0.006000 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 3 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 35.955997 ms (FindLiveObjects: 0.145100 ms CreateObjectMapping: 0.091900 ms MarkObjects: 35.586300 ms  DeleteObjects: 0.132000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 1 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.179302 ms (FindLiveObjects: 0.206500 ms CreateObjectMapping: 0.081600 ms MarkObjects: 35.825001 ms  DeleteObjects: 0.065700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 36.504501 ms (FindLiveObjects: 0.333800 ms CreateObjectMapping: 0.090900 ms MarkObjects: 36.074799 ms  DeleteObjects: 0.004200 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 36.130001 ms (FindLiveObjects: 0.179300 ms CreateObjectMapping: 0.092500 ms MarkObjects: 35.852200 ms  DeleteObjects: 0.005400 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.630398 ms (FindLiveObjects: 0.157500 ms CreateObjectMapping: 0.079400 ms MarkObjects: 36.387501 ms  DeleteObjects: 0.005500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 36.244900 ms (FindLiveObjects: 0.164200 ms CreateObjectMapping: 0.058800 ms MarkObjects: 36.016899 ms  DeleteObjects: 0.004100 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.194202 ms (FindLiveObjects: 0.154200 ms CreateObjectMapping: 0.081600 ms MarkObjects: 35.953102 ms  DeleteObjects: 0.004800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 35.493099 ms (FindLiveObjects: 0.171400 ms CreateObjectMapping: 0.091800 ms MarkObjects: 35.224602 ms  DeleteObjects: 0.004700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.625603 ms (FindLiveObjects: 0.171300 ms CreateObjectMapping: 0.086200 ms MarkObjects: 36.363503 ms  DeleteObjects: 0.004200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.568703 ms (FindLiveObjects: 0.190000 ms CreateObjectMapping: 0.091600 ms MarkObjects: 36.281197 ms  DeleteObjects: 0.005300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.354797 ms (FindLiveObjects: 0.215000 ms CreateObjectMapping: 0.086800 ms MarkObjects: 36.046902 ms  DeleteObjects: 0.005400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 35.687302 ms (FindLiveObjects: 0.172800 ms CreateObjectMapping: 0.086800 ms MarkObjects: 35.422600 ms  DeleteObjects: 0.004200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 35.583298 ms (FindLiveObjects: 0.188400 ms CreateObjectMapping: 0.080200 ms MarkObjects: 35.309700 ms  DeleteObjects: 0.004400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 34.729801 ms (FindLiveObjects: 0.178400 ms CreateObjectMapping: 0.092200 ms MarkObjects: 34.454201 ms  DeleteObjects: 0.004400 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 37.250999 ms (FindLiveObjects: 0.166200 ms CreateObjectMapping: 0.078400 ms MarkObjects: 37.001698 ms  DeleteObjects: 0.004300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 37.131599 ms (FindLiveObjects: 0.221000 ms CreateObjectMapping: 0.112000 ms MarkObjects: 36.792599 ms  DeleteObjects: 0.005400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5220.
Total: 36.541702 ms (FindLiveObjects: 0.216800 ms CreateObjectMapping: 0.080500 ms MarkObjects: 36.237499 ms  DeleteObjects: 0.006100 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 37.109798 ms (FindLiveObjects: 0.161600 ms CreateObjectMapping: 0.071700 ms MarkObjects: 36.871399 ms  DeleteObjects: 0.004600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 35.883698 ms (FindLiveObjects: 0.153400 ms CreateObjectMapping: 0.061000 ms MarkObjects: 35.663700 ms  DeleteObjects: 0.005000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5025.
Total: 36.257000 ms (FindLiveObjects: 0.235500 ms CreateObjectMapping: 0.095400 ms MarkObjects: 35.920898 ms  DeleteObjects: 0.004600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5187.
Total: 36.879601 ms (FindLiveObjects: 0.183600 ms CreateObjectMapping: 0.086000 ms MarkObjects: 36.604301 ms  DeleteObjects: 0.005100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5187.
Total: 35.870998 ms (FindLiveObjects: 0.220100 ms CreateObjectMapping: 0.081200 ms MarkObjects: 35.563396 ms  DeleteObjects: 0.005500 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 5189.
Total: 38.241901 ms (FindLiveObjects: 0.184200 ms CreateObjectMapping: 0.084700 ms MarkObjects: 37.952503 ms  DeleteObjects: 0.019800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5189.
Total: 37.422298 ms (FindLiveObjects: 0.143800 ms CreateObjectMapping: 0.078800 ms MarkObjects: 37.184200 ms  DeleteObjects: 0.014900 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5189.
Total: 36.566498 ms (FindLiveObjects: 0.171400 ms CreateObjectMapping: 0.100200 ms MarkObjects: 36.288101 ms  DeleteObjects: 0.006300 ms)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)
NullReferenceException: Object reference not set to an instance of an object
  at DialogUI.Update () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5033.
Total: 36.820000 ms (FindLiveObjects: 0.159800 ms CreateObjectMapping: 0.070100 ms MarkObjects: 36.584499 ms  DeleteObjects: 0.004800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5033.
Total: 35.598900 ms (FindLiveObjects: 0.133600 ms CreateObjectMapping: 0.079900 ms MarkObjects: 35.354000 ms  DeleteObjects: 0.030900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5035.
Total: 37.912201 ms (FindLiveObjects: 0.162800 ms CreateObjectMapping: 0.075600 ms MarkObjects: 37.667900 ms  DeleteObjects: 0.005100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5043.
Total: 36.846199 ms (FindLiveObjects: 0.160500 ms CreateObjectMapping: 0.082500 ms MarkObjects: 36.598499 ms  DeleteObjects: 0.004100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 37.262802 ms (FindLiveObjects: 0.165000 ms CreateObjectMapping: 0.072900 ms MarkObjects: 37.007401 ms  DeleteObjects: 0.017100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 37.378899 ms (FindLiveObjects: 0.148800 ms CreateObjectMapping: 0.073700 ms MarkObjects: 37.150902 ms  DeleteObjects: 0.004700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 36.562099 ms (FindLiveObjects: 0.169600 ms CreateObjectMapping: 0.064300 ms MarkObjects: 36.323902 ms  DeleteObjects: 0.003700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 37.606102 ms (FindLiveObjects: 0.145000 ms CreateObjectMapping: 0.065200 ms MarkObjects: 37.390900 ms  DeleteObjects: 0.004300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 36.989101 ms (FindLiveObjects: 0.146800 ms CreateObjectMapping: 0.070800 ms MarkObjects: 36.766701 ms  DeleteObjects: 0.004200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 38.240997 ms (FindLiveObjects: 0.154800 ms CreateObjectMapping: 0.069700 ms MarkObjects: 38.011303 ms  DeleteObjects: 0.004600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 37.203800 ms (FindLiveObjects: 0.160900 ms CreateObjectMapping: 0.069100 ms MarkObjects: 36.967697 ms  DeleteObjects: 0.005500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 37.407700 ms (FindLiveObjects: 0.142500 ms CreateObjectMapping: 0.070200 ms MarkObjects: 37.189201 ms  DeleteObjects: 0.005300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 5045.
Total: 36.317101 ms (FindLiveObjects: 0.136900 ms CreateObjectMapping: 0.079100 ms MarkObjects: 36.070599 ms  DeleteObjects: 0.029900 ms)

