--[[
金庸群侠传X 物品逻辑扩展

]]--
local Tools = luanet.import_type('JyGame.Tools')
local Debug = luanet.import_type('UnityEngine.Debug')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local CommonSettings = luanet.import_type('JyGame.CommonSettings')
local RuntimeData = luanet.import_type('JyGame.RuntimeData')
local Color = luanet.import_type('UnityEngine.Color')


--尝试使用物品(分解词条)
--说明：实现一个物品TRIGGER分两部分，一部分是在本函数定义，将结果存在itemResult里，另外一部分是
--在以下两个函数中实现。
function ITEM_OnTryUse(sourceRole, targetRole, itemTrigger, itemResult)
	--print("item trigger = "..itemTrigger)
	--itemResult.data["test"] = "1"

local ANIP={"asdg","baiyinv_quan","blnz","caoyuan","chengying","dadaoke","dama","dama2","daogu","daoke","dawu","diao","diyun","dongfang","dongxie","duguqiubai","emeici","fanseng","fanseng2","fengzi","gaoseng","gongsunzhi","guofu","guojing","guoxiang","heiyinv_gouzi","heiyiren","honglingbo","hongqigong","hongyinv","houzi","huaheshang","huangrong","huangyinv_jian","huodu","hyhs","hyjk","hynz","jianke","jianke_bai","jianke_lv","jinlunfawang","jinv","jylh","kezhenger","laoheshang","laotou_jian","limochou","lljk","luyoujiao","mayu","miaonv_quan","mojun","mutouren","nandi","njk","nqhs","nv_bai_quan","nv_hong_quan","nv_hua_quan","nv_lv_quan","nvliumang","nvpanjun","nvwu","nvyapu","ouyangfeng","qdtl","qiuchuji","qiuqianren","quanshi","quanzhennv","shibing","shijing","snyw","sunpopo","tjhf","wujiang","xiake","xiaofeng","xiaolongnv","xiedaoshi","xiedaoshi2","xuedaolaozu","yangdingtian","yinzhiping","ysxd","yufu","yuzhenzi","zhangwuji","zhaozhijing","zhongniannan","zhongniannan2","zhoubotong","zhuziliu","zydx","zyjk"}
local function RandomTable(Table)
	math.randomseed(os.time())
	return Table[math.random(1,#Table)]
end
	
if itemTrigger.Name == "addquanfa" then
itemResult.data["quanzhang"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addjianfa" then
itemResult.data["jianfa"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "haogan" then
itemResult.data["haogan"..itemTrigger.Argvs[0]] = tonumber(itemTrigger.Argvs[1])
elseif itemTrigger.Name == "adddaofa" then
itemResult.data["daofa"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addqimen" then
itemResult.data["qimen"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addbili" then
itemResult.data["bili"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addshenfa" then
itemResult.data["shenfa"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addwuxing" then
itemResult.data["wuxing"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addfuyuan" then
itemResult.data["fuyuan"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addgengu" then
itemResult.data["gengu"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "adddingli" then
itemResult.data["dingli"] = tonumber(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "addroundhp" then
		itemResult.data["newhp"] = tonumber(itemTrigger.Argvs[0])*RuntimeData.Instance.Round
elseif itemTrigger.Name == "addroundmp" then
		itemResult.data["newmp"] = tonumber(itemTrigger.Argvs[0])*RuntimeData.Instance.Round
elseif itemTrigger.Name == "触发剧情" then
		itemResult.data["剧情"] = tostring(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "女性" then
  itemResult.data["female"] = true
elseif itemTrigger.Name == "男性" then
  itemResult.data["male"] = true  
elseif itemTrigger.Name == "进入地图" then
		itemResult.data["地图"] = tostring(itemTrigger.Argvs[0])
--elseif itemTrigger.Name == "酒神" then
--itemResult.data["jiushen"] = true
elseif itemTrigger.Name == "脱胎换骨" then
		itemResult.data["zhuansheng"] = true
elseif itemTrigger.Name == "成长替换" then
		itemResult.data["grow"] = tostring(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "角色时装" then
		itemResult.data["jssz"] = tostring(itemTrigger.Argvs[0])
elseif itemTrigger.Name == "换装" then
itemResult.data["anim"] = tostring(ANIP[tonumber(itemTrigger.Argvs[0])])
elseif itemTrigger.Name == "随机换装" then
itemResult.data["anim1"] = tostring(RandomTable(ANIP))
	elseif itemTrigger.Name == "清除所有BUFF" then
		itemResult.data["clearbuffs"] = true
	elseif itemTrigger.Name == "提升定力" then
		itemResult.data["dingli"] = tonumber(itemTrigger.Argvs[0])
	elseif itemTrigger.Name == "清除所有BUFF" then
		itemResult.data["clearbuffs"] = true
	end
end


--执行战斗中使用物品结果(如加血、加蓝)
function ITEM_OnItemResultRun(result, targetSprite, battleField)
if(result.data["haogan魅力"] ~= nil) then
		if(targetSprite.Role.Key=="主角" ) then
			RuntimeData.Instance:addHaogan(result.data["haogan魅力"],"魅力")
			targetSprite:Refresh()
		end
end
if(result.data["haogan娇媚"] ~= nil) then
		if(targetSprite.Role.Key=="主角" ) then
			RuntimeData.Instance:addHaogan(result.data["haogan娇媚"],"娇媚")
			targetSprite:Refresh()
        end
end
if(result.data["haogan容貌"] ~= nil) then
		if(targetSprite.Role.Key=="主角" ) then
			RuntimeData.Instance:addHaogan(result.data["haogan容貌"],"容貌")
			targetSprite:Refresh()
        end
end
if(result.data["haogan身材"] ~= nil) then
		if(targetSprite.Role.Key=="主角" ) then
			RuntimeData.Instance:addHaogan(result.data["haogan身材"],"身材")
			targetSprite:Refresh()
        end
end
if(result.data["haogan药毒"] ~= nil) then
		if(targetSprite.Role.Key=="主角" ) then
			RuntimeData.Instance:addHaogan(result.data["haogan药毒"],"药毒")
			targetSprite:Refresh()
        end
end
	if(result.data["clearbuffs"] ~= nil) then
		targetSprite.Buffs:Clear()
		targetSprite:AttackInfo("清除所有BUFF", Color.red)
		targetSprite:Refresh()
	end
end

--执行永久提升性物品（如提升血内上限）
function ITEM_OnUseUpgradeItem(result, role)
--指定newmp的写入对象
if(result.data["newhp"] ~= nil) then
		role.maxhp = role.maxhp + result.data["newhp"]
	end
if(result.data["newmp"] ~= nil) then
		role.maxhp = role.maxhp + result.data["newmp"]
	end
--if(result.data["jiushen"] ~= nil) then
--role.jiushen_count = 0
--end
if(result.data["grow"] ~= nil) then
role.GrowTemplateValue = result.data["grow"]
end
if(result.data["zhuansheng"] ~= nil) then
role.level = 1
end
if(result.data["剧情"] ~= nil) then	    
RuntimeData.Instance.gameEngine:SwitchGameScene("story", result.data["剧情"])
AudioManager.Instance:PlayEffect("音效.内功攻击6")
return true
end
if(result.data["地图"] ~= nil) then	    
RuntimeData.Instance.gameEngine:SwitchGameScene("map", result.data["地图"])
AudioManager.Instance:PlayEffect("音效.内功攻击6")
return true
end
if(result.data["female"] ~= nil) then
role.FemaleValue = 1
end
if(result.data["jssz"] ~= nil) then
  role.animation = result.data["jssz"]
--Trigger的value为字符串 例："xiaolongnv"
	end
if(result.data["quanzhang"] ~= nil) then
  role.quanzhang = role.quanzhang + result.data["quanzhang"]
	end
if(result.data["jianfa"] ~= nil) then
		role.jianfa = role.jianfa + result.data["jianfa"]
	end
if(result.data["daofa"] ~= nil) then
		role.daofa = role.daofa + result.data["daofa"]
	end
if(result.data["qimen"] ~= nil) then
role.qimen = role.qimen + result.data["qimen"]
	end
if(result.data["wuxing"] ~= nil) then
role.wuxing = role.wuxing + result.data["wuxing"]
	end
if(result.data["shenfa"] ~= nil) then
	role.shenfa = role.shenfa + result.data["shenfa"]
	end
if(result.data["fuyuan"] ~= nil) then
	role.fuyuan = role.fuyuan + result.data["fuyuan"]
	end
if(result.data["gengu"] ~= nil) then
		role.gengu = role.gengu + result.data["gengu"]
	end
if(result.data["bili"] ~= nil) then
		role.bili = role.bili + result.data["bili"]
	end
if(result.data["dingli"] ~= nil) then
		role.dingli = role.dingli + result.data["dingli"]
	end
	if(result.data["anim1"] ~= nil) then
		role.animation =result.data["anim1"]
--Trigger的value可为空
AudioManager.Instance:PlayEffect("音效.内功攻击6")
	end
if(result.data["anim"] ~= nil) then
  role.animation = result.data["anim"]
--Trigger的value为指定序号 例："27"
	end
end