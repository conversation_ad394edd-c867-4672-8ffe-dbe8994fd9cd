<root>
  <trigger story="笑傲开始开关0">
    <condition type="exceed_day" value="1" />
  </trigger>
  <trigger story="倚天开始开关">
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="exceed_day" value="330" />
    <condition type="should_finish" value="倚天开始开关0" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="主角有恋人">
    <condition type="haogan_more_than" value="情人#1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="should_not_finish" value="主角有恋人" />
    <condition type="should_not_finish" value="主角有情人" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="主角有情人">
    <condition type="haogan_more_than" value="情人#2" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="should_finish" value="主角有恋人" />
    <condition type="should_not_finish" value="主角有情人" />
    <condition type="should_not_finish" value="主角有水性杨花" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="主角有水性杨花">
    <condition type="haogan_more_than" value="情人#5" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="should_finish" value="主角有情人" />
    <condition type="should_not_finish" value="主角有水性杨花" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <!-- <trigger story="作弊死亡3"> -->
    <!-- <condition type="haogan_more_than" value="善良#500" /> -->
  <!-- </trigger> -->
  <!-- <trigger story="作弊死亡4"> -->
    <!-- <condition type="haogan_more_than" value="胸围#200" /> -->
  <!-- </trigger> -->
  <!-- <trigger story="作弊死亡5"> -->
    <!-- <condition type="haogan_more_than" value="腰围#200" /> -->
  <!-- </trigger> -->
  <!-- <trigger story="作弊死亡6"> -->
    <!-- <condition type="haogan_more_than" value="臀围#200" /> -->
  <!-- </trigger> -->
  <!-- <trigger story="作弊死亡7"> -->
    <!-- <condition type="haogan_more_than" value="魅力#555" /> -->
  <!-- </trigger> -->
  <trigger story="小笨拳掌200">
    <condition type="quanzhang_greater_than" value="199" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="小笨剑法200">
    <condition type="jianfa_greater_than" value="199" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="小笨刀法200">
    <condition type="daofa_greater_than" value="199" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="小笨奇门200">
    <condition type="qimen_greater_than" value="199" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="心态恢复判断">
    <condition type="haogan_less_than" value="信心#50" />
    <condition type="should_finish" value="二阶段结局判断" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="毒术50">
    <condition type="haogan_more_than" value="毒术#50" />
    <condition type="should_not_finish" value="看五毒秘传" />
    <condition type="should_not_finish" value="毒术50李莫愁" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="毒术50李莫愁">
    <condition type="haogan_more_than" value="毒术#50" />
    <condition type="should_finish" value="看五毒秘传" />
    <condition type="should_not_finish" value="毒术50" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="药毒1">
    <condition type="haogan_more_than" value="药毒#25" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="药毒2">
    <condition type="haogan_more_than" value="药毒#50" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="药毒3">
    <condition type="haogan_more_than" value="药毒#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="毒术100">
    <condition type="haogan_more_than" value="毒术#70" />
    <condition type="should_not_finish" value="二阶段回家找2毒蛇练功" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="毒术200">
    <condition type="haogan_more_than" value="毒术#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="医术50">
    <condition type="haogan_more_than" value="医术#50" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="医术100">
    <condition type="haogan_more_than" value="医术#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="医术150">
    <condition type="haogan_more_than" value="医术#150" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="善良100">
    <condition type="haogan_more_than" value="善良#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="善良负100">
    <condition type="haogan_more_than" value="邪恶#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="魅力100">
    <condition type="haogan_more_than" value="魅力#330" />
    <condition type="exceed_day" value="250" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="魅力200">
    <condition type="haogan_more_than" value="魅力#350" />
    <condition type="exceed_day" value="250" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="胸大">
    <condition type="haogan_more_than" value="胸围#105" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="腰细1">
    <condition type="haogan_less_than" value="腰围#53" />
    <condition type="haogan_less_than" value="臀围#86" />
    <condition type="should_not_finish" value="臀粗1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="腰细2">
    <condition type="haogan_less_than" value="腰围#53" />
    <condition type="haogan_less_than" value="臀围#86" />
    <condition type="should_finish" value="臀粗1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="臀粗1">
    <condition type="haogan_more_than" value="臀围#90" />
    <condition type="haogan_more_than" value="胸围#90" />
    <condition type="should_not_finish" value="腰细1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="臀粗2">
    <condition type="haogan_more_than" value="臀围#90" />
    <condition type="haogan_more_than" value="胸围#90" />
    <condition type="should_finish" value="腰细1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="开放100">
    <condition type="haogan_more_than" value="淫荡#50" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="开放200">
    <condition type="haogan_more_than" value="淫荡#99" />
    <condition type="haogan_less_than" value="善良#50" />
    <condition type="exceed_day" value="250" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="琴艺50">
    <condition type="haogan_more_than" value="琴艺#49" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="裁缝100">
    <condition type="haogan_more_than" value="裁缝#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="习得勾魂指">
    <condition type="haogan_more_than" value="淫荡#50" />
    <condition type="haogan_more_than" value="百合#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="习得明玉功">
    <condition type="haogan_more_than" value="健康#99" />
    <condition type="haogan_more_than" value="善良#80" />
    <condition type="haogan_more_than" value="信心#99" />
    <condition type="haogan_less_than" value="淫荡#50" />
    <condition type="exceed_day" value="600" />
    <condition type="should_not_finish" value="习得销魂摄心大法" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="习得销魂摄心大法">
    <condition type="haogan_more_than" value="魅力#350" />
    <condition type="haogan_more_than" value="信心#99" />
    <condition type="haogan_more_than" value="心机#80" />
    <condition type="haogan_less_than" value="淫荡#99" />
    <condition type="should_not_finish" value="习得明玉功" />
    <condition type="exceed_day" value="600" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="习得紫气天罗">
    <condition type="haogan_more_than" value="健康#99" />
    <condition type="haogan_more_than" value="信心#99" />
    <condition type="haogan_more_than" value="邪恶#80" />
    <condition type="haogan_less_than" value="淫荡#99" />
    <condition type="exceed_day" value="600" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="才识魅力增加1">
    <condition type="haogan_more_than" value="才识#40" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="才识魅力增加2">
    <condition type="haogan_more_than" value="才识#70" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="才识魅力增加3">
    <condition type="haogan_more_than" value="才识#99" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="得到软烟罗裳">
    <condition type="have_item" value="软烟罗裳" />
    <condition type="should_not_finish" value="得到软烟罗裳" />
  </trigger>
  <trigger story="得到萝莉装">
    <condition type="have_item" value="萝莉装" />
    <condition type="should_not_finish" value="得到萝莉装" />
  </trigger>
  <trigger story="得到素绸朱绫">
    <condition type="have_item" value="素绸朱绫" />
    <condition type="should_not_finish" value="得到素绸朱绫" />
  </trigger>
  <trigger story="得到胧影织衣">
    <condition type="have_item" value="胧影织衣" />
    <condition type="should_not_finish" value="得到胧影织衣" />
  </trigger>
  <trigger story="得到翠羽黄衫">
    <condition type="have_item" value="翠羽黄衫" />
    <condition type="should_not_finish" value="得到翠羽黄衫" />
  </trigger>
  <trigger story="得到梦结萝殇">
    <condition type="have_item" value="梦结萝殇" />
    <condition type="should_not_finish" value="得到梦结萝殇" />
  </trigger>
  <trigger story="得到幽梦衣">
    <condition type="have_item" value="幽梦衣" />
    <condition type="should_not_finish" value="得到幽梦衣" />
  </trigger>
  <trigger story="得到青丝绣衣">
    <condition type="have_item" value="百花裙" />
    <condition type="should_not_finish" value="得到青丝绣衣" />
  </trigger>
  <trigger story="得到天瑞华绫">
    <condition type="have_item" value="天瑞华绫" />
    <condition type="should_not_finish" value="得到天瑞华绫" />
  </trigger>
  <trigger story="得到魅舞飘带">
    <condition type="have_item" value="魅舞飘带" />
    <condition type="should_not_finish" value="得到魅舞飘带" />
  </trigger>
  <trigger story="得到皇后的新衣">
    <condition type="have_item" value="皇后的新衣" />
    <condition type="should_not_finish" value="得到皇后的新衣" />
  </trigger>
  <trigger story="得到冰璃玄甲">
    <condition type="have_item" value="冰璃玄甲" />
    <condition type="should_not_finish" value="得到冰璃玄甲" />
  </trigger>
  <trigger story="得到紫薇仙衣">
    <condition type="have_item" value="紫薇仙衣" />
    <condition type="should_not_finish" value="得到紫薇仙衣" />
  </trigger>
  <trigger story="得到黄泉祭衣">
    <condition type="have_item" value="黄泉祭衣" />
    <condition type="should_not_finish" value="得到黄泉祭衣" />
  </trigger>
  <trigger story="服装收集成功">
    <condition type="haogan_more_than" value="服装数量#9" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="凤天南高利贷普通1000还钱">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="凤天南高利贷普通2000还钱">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="凤天南高利贷高魅力1000还钱">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="凤天南高利贷高魅力2000还钱">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="凤天南高利贷普通1000还钱2">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通1000还钱" />
  </trigger>
  <trigger story="凤天南高利贷普通2000还钱2">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通2000还钱" />
  </trigger>
  <trigger story="凤天南高利贷高魅力1000还钱2">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力1000还钱" />
  </trigger>
  <trigger story="凤天南高利贷高魅力2000还钱2">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力2000还钱" />
  </trigger>
  <trigger story="凤天南高利贷普通1000还钱3">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通1000还钱2" />
  </trigger>
  <trigger story="凤天南高利贷普通2000还钱3">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通2000还钱2" />
  </trigger>
  <trigger story="凤天南高利贷高魅力1000还钱3">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力1000还钱2" />
  </trigger>
  <trigger story="凤天南高利贷高魅力2000还钱3">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力2000还钱2" />
  </trigger>
  <trigger story="凤天南高利贷普通1000还钱4">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通1000还钱3" />
  </trigger>
  <trigger story="凤天南高利贷普通2000还钱4">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20001" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷普通2000还钱3" />
  </trigger>
  <trigger story="凤天南高利贷高魅力1000还钱4">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱10002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力1000还钱3" />
  </trigger>
  <trigger story="凤天南高利贷高魅力2000还钱4">
    <condition type="haogan_more_than" value="凤天南借钱#99" />
    <condition type="has_time_key" value="凤天南还钱20002" />
    <condition type="not_has_time_key" value="凤天南还钱" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="凤天南高利贷高魅力2000还钱3" />
  </trigger>
  <trigger story="怀孕康敏11">
    <condition type="not_has_time_key" value="怀孕康敏" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕康敏22">
    <condition type="not_has_time_key" value="怀孕康敏" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕康敏33">
    <condition type="not_has_time_key" value="怀孕康敏" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子康敏1">
    <condition type="not_has_time_key" value="怀孕康敏2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子康敏2">
    <condition type="not_has_time_key" value="怀孕康敏2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子康敏3">
    <condition type="not_has_time_key" value="怀孕康敏2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕康敏3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕郭靖11">
    <condition type="not_has_time_key" value="怀孕郭靖" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕郭靖22">
    <condition type="not_has_time_key" value="怀孕郭靖" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕郭靖33">
    <condition type="not_has_time_key" value="怀孕郭靖" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子郭靖1">
    <condition type="not_has_time_key" value="怀孕郭靖2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子郭靖2">
    <condition type="not_has_time_key" value="怀孕郭靖2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子郭靖3">
    <condition type="not_has_time_key" value="怀孕郭靖2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕郭靖3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕段誉11">
    <condition type="not_has_time_key" value="怀孕段誉" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕段誉22">
    <condition type="not_has_time_key" value="怀孕段誉" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕段誉33">
    <condition type="not_has_time_key" value="怀孕段誉" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子段誉1">
    <condition type="not_has_time_key" value="怀孕段誉2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子段誉2">
    <condition type="not_has_time_key" value="怀孕段誉2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子段誉3">
    <condition type="not_has_time_key" value="怀孕段誉2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕段誉3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕狄云11">
    <condition type="not_has_time_key" value="怀孕狄云" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕狄云22">
    <condition type="not_has_time_key" value="怀孕狄云" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕狄云33">
    <condition type="not_has_time_key" value="怀孕狄云" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子狄云1">
    <condition type="not_has_time_key" value="怀孕狄云2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子狄云2">
    <condition type="not_has_time_key" value="怀孕狄云2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子狄云3">
    <condition type="not_has_time_key" value="怀孕狄云2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕狄云3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕袁冠南11">
    <condition type="not_has_time_key" value="怀孕袁冠南" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕袁冠南22">
    <condition type="not_has_time_key" value="怀孕袁冠南" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕袁冠南33">
    <condition type="not_has_time_key" value="怀孕袁冠南" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子袁冠南1">
    <condition type="not_has_time_key" value="怀孕袁冠南2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子袁冠南2">
    <condition type="not_has_time_key" value="怀孕袁冠南2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子袁冠南3">
    <condition type="not_has_time_key" value="怀孕袁冠南2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕袁冠南3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕沈飞11">
    <condition type="not_has_time_key" value="怀孕沈飞" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕沈飞22">
    <condition type="not_has_time_key" value="怀孕沈飞" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕沈飞33">
    <condition type="not_has_time_key" value="怀孕沈飞" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子沈飞1">
    <condition type="not_has_time_key" value="怀孕沈飞2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子沈飞2">
    <condition type="not_has_time_key" value="怀孕沈飞2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子沈飞3">
    <condition type="not_has_time_key" value="怀孕沈飞2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕沈飞3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕欧阳克11">
    <condition type="not_has_time_key" value="怀孕欧阳克" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕欧阳克22">
    <condition type="not_has_time_key" value="怀孕欧阳克" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕欧阳克33">
    <condition type="not_has_time_key" value="怀孕欧阳克" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子欧阳克1">
    <condition type="not_has_time_key" value="怀孕欧阳克2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子欧阳克2">
    <condition type="not_has_time_key" value="怀孕欧阳克2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子欧阳克3">
    <condition type="not_has_time_key" value="怀孕欧阳克2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕欧阳克3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕齐云璈11">
    <condition type="not_has_time_key" value="怀孕齐云璈" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕齐云璈22">
    <condition type="not_has_time_key" value="怀孕齐云璈" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕齐云璈33">
    <condition type="not_has_time_key" value="怀孕齐云璈" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子齐云璈1">
    <condition type="not_has_time_key" value="怀孕齐云璈2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子齐云璈2">
    <condition type="not_has_time_key" value="怀孕齐云璈2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子齐云璈3">
    <condition type="not_has_time_key" value="怀孕齐云璈2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕齐云璈3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕姜小铁11">
    <condition type="not_has_time_key" value="怀孕姜小铁" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕姜小铁1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕姜小铁22">
    <condition type="not_has_time_key" value="怀孕姜小铁" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕姜小铁2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕姜小铁33">
    <condition type="not_has_time_key" value="怀孕姜小铁" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕姜小铁3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕按摩11">
    <condition type="not_has_time_key" value="怀孕按摩" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕按摩22">
    <condition type="not_has_time_key" value="怀孕按摩" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕按摩33">
    <condition type="not_has_time_key" value="怀孕按摩" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子按摩1">
    <condition type="not_has_time_key" value="怀孕按摩2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩11怀孕" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子按摩2">
    <condition type="not_has_time_key" value="怀孕按摩2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩22怀孕" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子按摩3">
    <condition type="not_has_time_key" value="怀孕按摩2" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕按摩33怀孕" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕宝象11">
    <condition type="not_has_time_key" value="怀孕宝象" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕宝象1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕宝象22">
    <condition type="not_has_time_key" value="怀孕宝象" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕宝象2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕宝象33">
    <condition type="not_has_time_key" value="怀孕宝象" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕宝象3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕小混混11">
    <condition type="not_has_time_key" value="怀孕小混混" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕小混混1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕小混混22">
    <condition type="not_has_time_key" value="怀孕小混混" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕小混混2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕小混混33">
    <condition type="not_has_time_key" value="怀孕小混混" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕小混混3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕番僧11">
    <condition type="not_has_time_key" value="怀孕番僧" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕番僧1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕番僧22">
    <condition type="not_has_time_key" value="怀孕番僧" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕番僧2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕番僧33">
    <condition type="not_has_time_key" value="怀孕番僧" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕番僧3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕薛慕华2">
    <condition type="not_has_time_key" value="怀孕薛慕华" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕薛慕华" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕游坦之2">
    <condition type="not_has_time_key" value="怀孕游坦之" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕游坦之" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕田归农2">
    <condition type="not_has_time_key" value="怀孕田归农" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕田归农" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕商人2">
    <condition type="not_has_time_key" value="怀孕商人" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕商人" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕群交11">
    <condition type="not_has_time_key" value="怀孕群交" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕群交1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕群交22">
    <condition type="not_has_time_key" value="怀孕群交" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕群交2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕群交33">
    <condition type="not_has_time_key" value="怀孕群交" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕群交3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕群交44">
    <condition type="not_has_time_key" value="怀孕群交" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕群交4" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕妓女11">
    <condition type="not_has_time_key" value="怀孕妓女" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕妓女1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕妓女22">
    <condition type="not_has_time_key" value="怀孕妓女" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕妓女2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕妓女33">
    <condition type="not_has_time_key" value="怀孕妓女" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕妓女3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="怀孕妓女44">
    <condition type="not_has_time_key" value="怀孕妓女" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕妓女4" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子间男1">
    <condition type="not_has_time_key" value="怀孕间男" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕期1" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子间男2">
    <condition type="not_has_time_key" value="怀孕间男" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕期2" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子间男3">
    <condition type="not_has_time_key" value="怀孕间男" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕期3" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子间男4">
    <condition type="not_has_time_key" value="怀孕间男" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕期4" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子间男5">
    <condition type="not_has_time_key" value="怀孕间男" />
    <condition type="has_time_key" value="生育" />
    <condition type="should_finish" value="怀孕期5" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="开始4">
    <condition type="exceed_day" value="5" />
  </trigger>
  <trigger story="开始5">
    <condition type="exceed_day" value="6" />
  </trigger>
  <trigger story="男主主线2">
    <condition type="exceed_day" value="14" />
  </trigger>
  <trigger story="危险期说明">
    <condition type="exceed_day" value="19" />
  </trigger>
  <trigger story="男主主线5">
    <condition type="exceed_day" value="26" />
  </trigger>
  <trigger story="男主主线DEBUG">
    <condition type="should_finish" value="男主主线9" />
  </trigger>
  <trigger story="男主主线5不去">
    <condition type="exceed_day" value="33" />
    <condition type="should_finish" value="男主主线5" />
    <condition type="should_not_finish" value="男主主线6" />
    <condition type="should_not_finish" value="西侧山洞15" />
    <condition type="should_not_finish" value="男主主线5不去" />
  </trigger>
  <trigger story="金盆洗手1">
    <condition type="exceed_day" value="42" />
  </trigger>
  <trigger story="危险期1">
    <condition type="exceed_day" value="49" />
  </trigger>
  <trigger story="慕容复到访">
    <condition type="exceed_day" value="58" />
    <condition type="should_not_finish" value="阿青主线11" />
  </trigger>
  <trigger story="阿青开始">
    <condition type="exceed_day" value="64" />
  </trigger>
  <trigger story="男主方云华">
    <condition type="exceed_day" value="67" />
  </trigger>
  <trigger story="阿青主线1">
    <condition type="exceed_day" value="70" />
  </trigger>
  <trigger story="阿青主线9">
    <condition type="exceed_day" value="75" />
  </trigger>
  <trigger story="危险期2">
    <condition type="exceed_day" value="79" />
  </trigger>
  <trigger story="阿青主线11">
    <condition type="exceed_day" value="84" />
  </trigger>
  <trigger story="百合花开1">
    <condition type="haogan_more_than" value="百合#50" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="百合花开2">
    <condition type="haogan_more_than" value="百合#100" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="阿青主线19">
    <condition type="exceed_day" value="92" />
  </trigger>
  <trigger story="月经1">
    <condition type="exceed_day" value="95" />
  </trigger>
  <trigger story="月经2">
    <condition type="exceed_day" value="40" />
    <condition type="should_finish" value="男主主线强奸1" />
  </trigger>
  <trigger story="石淇主线1">
    <condition type="exceed_day" value="100" />
  </trigger>
  <trigger story="洛阳花会1">
    <condition type="exceed_day" value="105" />
  </trigger>
  <trigger story="去洛阳DEBUG">
    <condition type="exceed_day" value="106" />
    <condition type="should_finish" value="洛阳花会3" />
  </trigger>
  <trigger story="序章门派图册">
    <condition type="exceed_day" value="132" />
  </trigger>
  <trigger story="白飞飞母亲">
    <condition type="exceed_day" value="138" />
  </trigger>
  <trigger story="石淇主线2开始">
    <condition type="exceed_day" value="145" />
  </trigger>
  <trigger story="门派_上官仙1">
    <condition type="exceed_day" value="164" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="灵鹫DEBUG">
    <condition type="in_menpai" value="灵鹫" />
    <condition type="exceed_day" value="150" />
  </trigger>
  <trigger story="一阶段感伤">
    <condition type="exceed_day" value="176" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段写信上官仙">
    <condition type="exceed_day" value="189" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="202" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段通知2">
    <condition type="exceed_day" value="210" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="符敏仪下山1">
    <condition type="exceed_day" value="218" />
    <condition type="should_finish" value="符敏仪考试成功" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="226" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="灵鹫下山前">
    <condition type="exceed_day" value="234" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="灵鹫出师">
    <condition type="exceed_day" value="241" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="248" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="一阶段回家开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="exceed_day" value="268" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="菊剑质疑1">
    <condition type="exceed_day" value="279" />
    <condition type="should_finish" value="李秋水下山1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫来信">
    <condition type="exceed_day" value="295" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="李秋水下山1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫来信李秋水">
    <condition type="exceed_day" value="301" />
    <condition type="should_finish" value="李秋水下山1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="310" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="风月小筑重建">
    <condition type="exceed_day" value="320" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="一阶段回家" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="365" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="382" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="395" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="425" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="435" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="457" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫李秋水见菊剑">
    <condition type="exceed_day" value="478" />
    <condition type="should_finish" value="菊剑坦白" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_finish" value="玄女2" />
    <condition type="exceed_day" value="504" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫英雄会前夕">
    <condition type="exceed_day" value="517" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="古墓DEBUG">
    <condition type="in_menpai" value="古墓" />
    <condition type="exceed_day" value="150" />
  </trigger>
  <trigger story="一阶段感伤">
    <condition type="exceed_day" value="178" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="古墓见甄志丙">
    <condition type="exceed_day" value="183" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段写信上官仙">
    <condition type="exceed_day" value="193" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="205" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段通知2">
    <condition type="exceed_day" value="222" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="248" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="主角杨过李莫愁来袭1">
    <condition type="exceed_day" value="289" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="主角杨过救小龙女2" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="主角单身李莫愁来袭1">
    <condition type="exceed_day" value="289" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="小龙女走火入魔5" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="主角小龙女李莫愁来袭1">
    <condition type="exceed_day" value="289" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="主角走火入魔5" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="李莫愁来袭1">
    <condition type="exceed_day" value="289" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="主角走火入魔5" />
    <condition type="should_not_finish" value="小龙女走火入魔5" />
    <condition type="should_not_finish" value="主角杨过救小龙女2" />
    <condition type="should_not_finish" value="修炼玉女心经失败3后续" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="李莫愁拒绝来袭1">
    <condition type="exceed_day" value="289" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="李莫愁拒绝" />
    <condition type="should_not_finish" value="主角走火入魔5" />
    <condition type="should_not_finish" value="小龙女走火入魔5" />
    <condition type="should_not_finish" value="主角杨过救小龙女2" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="李莫愁合作来袭1">
    <condition type="exceed_day" value="280" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="李莫愁合作" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="294" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="一阶段回家开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="305" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="东方不败死亡1" />
    <condition type="exceed_day" value="315" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="风月小筑重建">
    <condition type="exceed_day" value="360" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="一阶段回家" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="375" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="388" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="405" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="435" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="450" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="468" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="古墓英雄会前夕小龙女">
    <condition type="exceed_day" value="489" />
    <condition type="in_menpai" value="古墓" />
    <condition type="in_team" value="小龙女" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="古墓英雄会前夕杨过">
    <condition type="exceed_day" value="489" />
    <condition type="in_menpai" value="古墓" />
    <condition type="in_team" value="杨过" />
    <condition type="not_in_team" value="小龙女" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="古墓英雄会前夕自己">
    <condition type="exceed_day" value="489" />
    <condition type="in_menpai" value="古墓" />
    <condition type="in_team" value="李莫愁" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="玄女2" />
    <condition type="exceed_day" value="515" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="何铁手蓝凤凰演示武功">
    <condition type="exceed_day" value="171" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段感伤">
    <condition type="exceed_day" value="181" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="夏青青2">
    <condition type="not_has_time_key" value="当监狱长" />
    <condition type="should_finish" value="夏青青1" />
    <condition type="should_not_finish" value="何铁手4" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="袁承志3">
    <condition type="not_has_time_key" value="袁承志后续" />
    <condition type="should_finish" value="袁承志1" />
    <condition type="should_not_finish" value="何铁手4" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="一阶段写信上官仙">
    <condition type="exceed_day" value="193" />
    <condition type="in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="205" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="夏青青4">
    <condition type="not_has_time_key" value="夏公子陪何铁手" />
    <condition type="exceed_day" value="215" />
    <condition type="should_finish" value="夏青青3" />
    <condition type="should_not_finish" value="何铁手4" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="袁承志追求成功">
    <condition type="should_finish" value="袁承志5好感" />
    <condition type="haogan_more_than" value="袁承志#40" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="五毒门派比武">
    <condition type="exceed_day" value="230" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="夏青青5">
    <condition type="not_has_time_key" value="夏公子陪何铁手2" />
    <condition type="exceed_day" value="243" />
    <condition type="should_finish" value="夏青青4" />
    <condition type="should_not_finish" value="何铁手4" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="夏青青百合觉醒">
    <condition type="should_finish" value="夏青青5" />
    <condition type="should_finish" value="夏青青3百合" />
    <condition type="haogan_more_than" value="温青青#20" />
    <condition type="haogan_more_than" value="百合#50" />
    <condition type="in_menpai" value="五毒" />
    <condition type="exceed_day" value="248" />
    <condition type="should_not_finish" value="夏青青8" />
  </trigger>
  <trigger story="一阶段通知2">
    <condition type="exceed_day" value="257" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="何铁手4">
    <condition type="exceed_day" value="269" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="275" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="夏青青10">
    <condition type="exceed_day" value="281" />
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="失心人调查1">
    <condition type="exceed_day" value="287" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="失心人调查2">
    <condition type="exceed_day" value="293" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦6">
    <condition type="exceed_day" value="304" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦7">
    <condition type="exceed_day" value="308" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦6" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦8">
    <condition type="exceed_day" value="312" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦7" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦11">
    <condition type="exceed_day" value="317" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦10" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒沧州大会">
    <condition type="exceed_day" value="320" />
    <condition type="should_not_finish" value="独孤伊人啦啦啦10" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="335" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="一阶段回家开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦14">
    <condition type="exceed_day" value="354" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦13" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦15">
    <condition type="exceed_day" value="358" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦13" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="独孤伊人啦啦啦16">
    <condition type="exceed_day" value="365" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦13" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒日月事件1">
    <condition type="exceed_day" value="377" />
    <condition type="should_finish" value="五毒教沦陷唐蓝2" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒日月事件结束">
    <condition type="should_finish" value="五毒日月事件2" />
    <condition type="should_finish" value="五毒日月事件3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="exceed_day" value="384" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="392" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="402" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="一阶段掌门人大会8五毒2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="412" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="422" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="455" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="独孤伊人啦啦啦17">
    <condition type="exceed_day" value="445" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="独孤伊人啦啦啦13" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="475" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="should_not_finish" value="一阶段掌门人大会8五毒正" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="487" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="一阶段掌门人大会8五毒2" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="玄女2" />
    <condition type="exceed_day" value="514" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="五毒日月事件死亡">
    <condition type="exceed_day" value="700" />
    <condition type="should_finish" value="五毒教沦陷唐蓝2" />
    <condition type="should_not_finish" value="五毒日月事件结束" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="日月教进攻五毒教开始">
    <condition type="exceed_day" value="760" />
    <condition type="should_finish" value="五毒日月事件结束" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="日月教进攻五毒教1">
    <condition type="exceed_day" value="820" />
    <condition type="should_finish" value="日月教进攻五毒教开始" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒少年大会预告">
    <condition type="exceed_day" value="480" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒解除巫烬开始">
    <condition type="have_item" value="莽牯朱蛤" />
    <condition type="have_item" value="断肠草" />
    <condition type="have_item" value="七心海棠" />
    <condition type="should_not_finish" value="夺回五毒教4" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒询问解法1">
    <condition type="not_has_time_key" value="五毒解毒预告" />
    <condition type="should_finish" value="五毒再次求援1" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒进攻中原">
    <condition type="exceed_day" value="900" />
    <condition type="should_not_finish" value="夺回五毒教开始" />
    <condition type="should_not_finish" value="夺回五毒教4" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒失心人事件2">
    <condition type="should_finish" value="佛山失心人1" />
    <condition type="should_finish" value="成都失心人1" />
    <condition type="should_finish" value="江陵失心人1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒失心人事件2别派">
    <condition type="should_finish" value="佛山失心人1" />
    <condition type="should_finish" value="成都失心人1" />
    <condition type="should_finish" value="江陵失心人1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="星宿派来袭1">
    <condition type="should_finish" value="成为五毒教主" />
    <condition type="should_finish" value="五毒武当宴会结束" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="星宿派来袭2">
    <condition type="should_finish" value="星宿派来袭1" />
    <condition type="not_has_time_key" value="星宿派偷袭五毒教" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五毒教走向">
    <condition type="should_finish" value="星宿派来袭2" />
    <condition type="not_has_time_key" value="决定五毒教走向" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="大战星宿派">
    <condition type="should_finish" value="五毒教走向西域线" />
    <condition type="should_not_finish" value="大战星宿派" />
    <condition type="not_has_time_key" value="对战星宿派" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="日月线开始2">
    <condition type="should_finish" value="日月线开始后续" />
    <condition type="not_has_time_key" value="加入日月" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段感伤">
    <condition type="exceed_day" value="188" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="学习日月武功桑三娘">
    <condition type="should_finish" value="学习日月心经桑三娘" />
    <condition type="not_has_time_key" value="桑三娘教外功" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="桑三娘1">
    <condition type="should_finish" value="学习日月心经桑三娘" />
    <condition type="exceed_day" value="222" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="初遇任盈盈">
    <condition type="should_finish" value="学习日月武功桑三娘" />
    <condition type="exceed_day" value="208" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="给任盈盈笑傲江湖曲开始">
    <condition type="should_finish" value="和任盈盈学琴日常" />
    <condition type="haogan_more_than" value="任盈盈#19" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="231" />
    <condition type="should_not_finish" value="任我行监视1" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="鲍大楚骚扰">
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="任我行监视1" />
    <condition type="exceed_day" value="240" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="248" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="任我行监视1" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="任盈盈比武">
    <condition type="should_finish" value="初遇任盈盈2普通" />
    <condition type="exceed_day" value="255" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="任盈盈比武">
    <condition type="should_finish" value="初遇任盈盈后续2" />
    <condition type="exceed_day" value="255" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="和任盈盈去洛阳">
    <condition type="should_finish" value="任盈盈比武" />
    <condition type="exceed_day" value="263" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="和任盈盈去洛阳2">
    <condition type="should_finish" value="和任盈盈去洛阳" />
    <condition type="exceed_day" value="270" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段通知2">
    <condition type="exceed_day" value="306" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="桑三娘感慨">
    <condition type="should_finish" value="学习日月武功桑三娘" />
    <condition type="exceed_day" value="315" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="英雄会前夕事件">
    <condition type="should_finish" value="和任盈盈去洛阳2" />
    <condition type="exceed_day" value="326" />
    <condition type="should_not_finish" value="日月战嵩山1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月主角找令狐冲5">
    <condition type="should_finish" value="日月主角找令狐冲" />
    <condition type="exceed_day" value="349" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月任盈盈找令狐冲6">
    <condition type="should_finish" value="日月任盈盈找令狐冲5" />
    <condition type="exceed_day" value="349" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="368" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月任盈盈线英雄会开始">
    <condition type="should_finish" value="英雄会前夕事件" />
    <condition type="exceed_day" value="360" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="和任盈盈去洛阳4DEBUG">
    <condition type="should_finish" value="和任盈盈去洛阳4" />
  </trigger>
  <trigger story="和任盈盈去洛阳8任盈盈DEBUG">
    <condition type="should_finish" value="和任盈盈去洛阳8任盈盈把脉3" />
  </trigger>
  <trigger story="和任盈盈去洛阳8任盈盈DEBUG">
    <condition type="should_finish" value="和任盈盈去洛阳8主角把脉2" />
  </trigger>
  <trigger story="和任盈盈去洛阳8任盈盈DEBUG">
    <condition type="should_finish" value="和任盈盈去洛阳8任盈盈把脉2" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="exceed_day" value="376" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="390" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="402" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="411" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="420" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="455" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="475" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="486" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="should_not_finish" value="学习日月心经" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="东方不败死亡5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="学习日月武功">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="not_has_time_key" value="东方不败教外功" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="初遇明月心">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="205" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="任我行监视1">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="213" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="鲍大楚骚扰">
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="任我行监视1" />
    <condition type="exceed_day" value="223" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="230" />
    <condition type="should_finish" value="任我行监视1" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="初遇任盈盈后续判断">
    <condition type="should_finish" value="初遇任盈盈2" />
    <condition type="not_has_time_key" value="初遇任盈盈" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="给明月心笑傲江湖曲开始">
    <condition type="should_finish" value="初遇明月心" />
    <condition type="haogan_more_than" value="任盈盈#30" />
    <condition type="not_has_time_key" value="明月心间隔" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="东方不败指点2">
    <condition type="should_finish" value="任我行监视1" />
    <condition type="exceed_day" value="238" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="248" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="任我行监视1" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="东方不败检验武功">
    <condition type="should_finish" value="任我行监视1" />
    <condition type="exceed_day" value="255" />
  </trigger>
  <trigger story="任我行监视2">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="267" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月白飞飞思念">
    <condition type="exceed_day" value="274" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="任我行监视1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="日月战嵩山开始">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="280" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="学习黑血神针">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="291" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="嵩山战斗前找明月心2">
    <condition type="should_finish" value="嵩山战斗前找明月心" />
    <condition type="exceed_day" value="295" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="嵩山战斗前找明月心3">
    <condition type="should_finish" value="嵩山战斗前找明月心2" />
    <condition type="exceed_day" value="305" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月战嵩山开始支线">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="300" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月战嵩山1">
    <condition type="should_finish" value="学习日月心经" />
    <condition type="exceed_day" value="310" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="东方不败高好感询问1">
    <condition type="should_finish" value="日月战嵩山1" />
    <condition type="haogan_more_than" value="东方不败#25" />
    <condition type="exceed_day" value="318" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段通知2">
    <condition type="exceed_day" value="325" />
    <condition type="should_finish" value="学习日月心经" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月战嵩山6PLUS">
    <condition type="should_finish" value="日月战嵩山6" />
    <condition type="exceed_day" value="333" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月战嵩山7">
    <condition type="should_finish" value="日月战嵩山1" />
    <condition type="should_not_finish" value="东方不败死亡1" />
    <condition type="exceed_day" value="343" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="找到曲非烟">
    <condition type="should_finish" value="夷光9PLUS" />
    <condition type="exceed_day" value="394" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
  </trigger>
  <trigger story="明月心高好感后续4">
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="exceed_day" value="402" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_not_finish" value="东方不败死亡1" />
    <condition type="exceed_day" value="434" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="404" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_not_finish" value="一阶段回家开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="445" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="422" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="学习日月心经" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段回家开启">
    <condition type="exceed_day" value="423" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_not_finish" value="一阶段回家开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="连城诀自动触发">
    <condition type="exceed_day" value="428" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_not_finish" value="探望丁典凌霜华" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_not_finish" value="东方不败死亡1" />
    <condition type="exceed_day" value="433" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="445" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="上官仙来访" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_finish" value="风月小筑重建" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="456" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="475" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="475" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="507" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="507" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="517" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="517" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="526" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="should_finish" value="明月心高好感后续3" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="should_not_finish" value="东方不败死亡5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月教英雄会前">
    <condition type="should_finish" value="东方不败死亡5" />
    <condition type="exceed_day" value="365" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月白飞飞死亡">
    <condition type="should_finish" value="日月教英雄会前" />
    <condition type="exceed_day" value="400" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月练兵">
    <condition type="should_finish" value="日月教英雄会前" />
    <condition type="exceed_day" value="430" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="东方不败死亡8">
    <condition type="should_finish" value="日月练兵" />
    <condition type="exceed_day" value="450" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月练兵嵩山">
    <condition type="should_finish" value="东方不败死亡8" />
    <condition type="exceed_day" value="471" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="东方不败死亡7">
    <condition type="should_finish" value="日月练兵嵩山" />
    <condition type="exceed_day" value="502" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="玄女2" />
    <condition type="exceed_day" value="519" />
    <condition type="in_menpai" value="日月" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期3">
    <condition type="exceed_day" value="169" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期4">
    <condition type="exceed_day" value="199" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段通知1">
    <condition type="exceed_day" value="186" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="一阶段通知1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段峨眉通知">
    <condition type="exceed_day" value="194" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="一阶段峨眉1">
    <condition type="exceed_day" value="235" />
    <condition type="should_finish" value="一阶段峨眉通知" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="正常事件不能发生" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="危险期5">
    <condition type="exceed_day" value="229" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="连城诀_戚芳进城">
    <condition type="should_finish" value="连城诀_初遇戚芳4" />
    <condition type="not_has_time_key" value="看望戚芳" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期6">
    <condition type="exceed_day" value="259" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="玄女2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="exceed_day" value="280" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期7">
    <condition type="exceed_day" value="289" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期8">
    <condition type="exceed_day" value="319" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="书剑公告1">
    <condition type="should_finish" value="书剑霍青桐12" />
    <condition type="not_has_time_key" value="沧州公告" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="书剑公告1">
    <condition type="should_finish" value="书剑提前结束" />
    <condition type="not_has_time_key" value="沧州公告" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="书剑公告1">
    <condition type="should_finish" value="书剑骆冰3" />
    <condition type="not_has_time_key" value="沧州公告" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="书剑公告1">
    <condition type="should_finish" value="书剑骆冰12" />
    <condition type="not_has_time_key" value="沧州公告" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="书剑公告1">
    <condition type="should_finish" value="书剑骆冰15" />
    <condition type="not_has_time_key" value="沧州公告" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="一阶段开始">
    <condition type="exceed_day" value="340" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期9">
    <condition type="exceed_day" value="349" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="月经3">
    <condition type="exceed_day" value="356" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="should_finish" value="一阶段掌门人大会3" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉1">
    <condition type="exceed_day" value="365" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙泡沈飞">
    <condition type="exceed_day" value="384" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="上官仙男主在一起" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人预告">
    <condition type="exceed_day" value="395" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="上官仙预告" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉杀人开始">
    <condition type="exceed_day" value="425" />
    <condition type="should_finish" value="帮助上官仙开启" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段上官仙失败">
    <condition type="exceed_day" value="473" />
    <condition type="should_not_finish" value="二阶段峨眉杀人" />
    <condition type="not_has_time_key" value="上官仙杀人" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_finish" value="二阶段峨眉1" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="上官仙男主在一起2">
    <condition type="exceed_day" value="496" />
    <condition type="should_not_finish" value="帮助上官仙开启" />
    <condition type="should_not_finish" value="男主喜欢3" />
    <condition type="haogan_more_than" value="上官仙男主#35" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="东方不败死亡5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玄女3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_finish" value="玄女2" />
    <condition type="exceed_day" value="516" />
    <condition type="not_in_menpai" value="古墓" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期10">
    <condition type="exceed_day" value="379" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="段誉木婉清后续">
    <condition type="should_finish" value="tlbb.dy_木婉清身世1" />
    <condition type="not_has_time_key" value="段誉追木婉清" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="笑傲华山养成1">
    <condition type="should_finish" value="笑傲福州结束" />
    <condition type="should_not_finish" value="拿辟邪剑法成功" />
    <condition type="not_has_time_key" value="华山养成开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="not_has_time_key" value="无法怀孕" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期11">
    <condition type="exceed_day" value="409" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="白雕送信">
    <condition type="should_finish" value="醉仙楼3主角黄蓉3" />
    <condition type="not_has_time_key" value="郭靖寄信" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段峨眉控制2">
    <condition type="exceed_day" value="490" />
    <condition type="should_finish" value="二阶段峨眉控制" />
    <condition type="should_not_finish" value="二阶段英雄会开始" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期12">
    <condition type="exceed_day" value="439" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期13">
    <condition type="exceed_day" value="469" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期14">
    <condition type="exceed_day" value="499" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期15">
    <condition type="exceed_day" value="529" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="凌霜华自己回家">
    <condition type="exceed_day" value="520" />
    <condition type="should_finish" value="凌霜华3不杀" />
    <condition type="should_not_finish" value="凌霜华自己回家" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="凌霜华自己回家">
    <condition type="not_has_time_key" value="凌霜华回家" />
    <condition type="should_finish" value="二阶段上官仙失败" />
    <condition type="should_finish" value="凌霜华3不杀" />
    <condition type="should_not_finish" value="凌霜华自己回家" />
    <condition type="should_not_finish" value="凌霜华回家1" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段开始提醒">
    <condition type="exceed_day" value="531" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期16">
    <condition type="exceed_day" value="559" />
    <condition type="should_not_finish" value="夷光4" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期17">
    <condition type="exceed_day" value="589" />
    <condition type="should_not_finish" value="夷光4" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期18">
    <condition type="exceed_day" value="619" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期19">
    <condition type="exceed_day" value="649" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期20">
    <condition type="exceed_day" value="679" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="康敏求助">
    <condition type="should_finish" value="异域商人3" />
    <condition type="haogan_more_than" value="魅力#330" />
    <condition type="haogan_less_than" value="善良#-50" />
    <condition type="haogan_more_than" value="信心#30" />
    <condition type="haogan_more_than" value="心魔#9" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="不能进行其他剧情" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段开始">
    <condition type="exceed_day" value="700" />
    <condition type="should_finish" value="二阶段开始提醒" />
    <condition type="should_not_finish" value="不参加试剑大会" />
    <condition type="should_not_finish" value="二阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月任盈盈线英雄会结束">
    <condition type="should_finish" value="拜师桑三娘" />
    <condition type="should_finish" value="领完奖品" />
    <condition type="should_not_finish" value="二阶段日月结局任盈盈" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="二阶段日月结局曲非烟">
    <condition type="should_finish" value="日月教英雄会前2" />
    <condition type="should_finish" value="领完奖品" />
    <condition type="should_not_finish" value="二阶段日月结局曲非烟" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="月经4">
    <condition type="exceed_day" value="745" />
    <condition type="should_finish" value="二阶段英雄会开始" />
    <condition type="should_not_finish" value="东方不败死亡7" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="夷光4" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="凌霜华自杀">
    <condition type="in_team" value="凌霜华丑" />
    <condition type="haogan_more_than" value="情人#2" />
    <condition type="haogan_less_than" value="凌霜华#70" />
    <condition type="not_has_time_key" value="凌霜华判断" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子水笙1">
    <condition type="in_team" value="水笙" />
    <condition type="should_finish" value="水笙会怀孕" />
    <condition type="should_not_finish" value="黑化水笙加入" />
    <condition type="should_not_finish" value="生孩子水笙2" />
    <condition type="not_has_time_key" value="水笙怀孕" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="生孩子水笙2">
    <condition type="should_finish" value="黑化水笙加入" />
    <condition type="in_team" value="水笙黑" />
    <condition type="should_not_finish" value="生孩子水笙1" />
    <condition type="should_finish" value="水笙会怀孕" />
    <condition type="not_has_time_key" value="水笙怀孕" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="沈飞成为男友">
    <condition type="should_finish" value="男主首次做爱2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="沈飞成为男友">
    <condition type="should_finish" value="一阶段男主2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="沈飞不再是男友">
    <condition type="should_finish" value="二阶段英雄会邪线男主事件" />
    <condition type="should_finish" value="沈飞成为男友" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="沈飞不再是男友">
    <condition type="should_finish" value="二阶段男主事件NTR3" />
    <condition type="should_finish" value="沈飞成为男友" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="有恋人">
    <condition type="should_finish" value="有女友" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="有恋人">
    <condition type="should_finish" value="有男友" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="危险期22">
    <condition type="exceed_day" value="739" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="门派灵鹫宫_出师">
    <condition type="exceed_day" value="755" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="灵鹫宫_委屈求全1" />
    <condition type="should_finish" value="二阶段结局判断" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="夷光4" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月东方善良线1">
    <condition type="exceed_day" value="780" />
    <condition type="should_finish" value="日月善良路线" />
    <condition type="should_finish" value="拜师东方不败" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月被围攻1">
    <condition type="exceed_day" value="850" />
    <condition type="should_finish" value="东方不败死亡5" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫背叛1">
    <condition type="exceed_day" value="833" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="玉玲珑13" />
    <condition type="should_not_finish" value="灵鹫宫_委屈求全" />
    <condition type="should_not_finish" value="灵鹫宫_攻打" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="青楼王语嫣1">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架" />
    <condition type="should_finish" value="西夏王语嫣虚竹救人3" />
  </trigger>
  <trigger story="青楼王语嫣2">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架2" />
    <condition type="should_finish" value="青楼王语嫣1" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣3">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架3" />
    <condition type="should_finish" value="青楼王语嫣2" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣4">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架4" />
    <condition type="should_finish" value="青楼王语嫣3" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣5">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架5" />
    <condition type="should_finish" value="青楼王语嫣4" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣6">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架6" />
    <condition type="should_finish" value="青楼王语嫣5" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣7">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架7" />
    <condition type="should_finish" value="青楼王语嫣6" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣8">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架8" />
    <condition type="should_finish" value="青楼王语嫣7" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="青楼王语嫣9">
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="王语嫣绑架9" />
    <condition type="should_finish" value="青楼王语嫣8" />
    <condition type="should_not_finish" value="营救王语嫣开始" />
  </trigger>
  <trigger story="郭靖成为男友">
    <condition type="should_finish" value="救出黄蓉6" />
    <condition type="not_has_time_key" value="郭靖男友" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="黄蓉死亡">
    <condition type="should_finish" value="铁掌山5" />
    <condition type="not_has_time_key" value="黄蓉死亡" />
    <condition type="should_not_finish" value="九阴真经1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="笑傲华山养成8">
    <condition type="exceed_day" value="750" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="should_finish" value="笑傲华山养成7" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期23">
    <condition type="exceed_day" value="769" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期24">
    <condition type="exceed_day" value="799" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期25">
    <condition type="exceed_day" value="829" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期26">
    <condition type="exceed_day" value="859" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="香香利用结束后续">
    <condition type="should_finish" value="香香利用7" />
    <condition type="not_has_time_key" value="姐妹花离开" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="明月心高好感后续11">
    <condition type="should_finish" value="明月心高好感后续10" />
    <condition type="exceed_day" value="815" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="明月心低好感路线后续3">
    <condition type="exceed_day" value="825" />
    <condition type="should_finish" value="明月心低好感路线后续2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="越女剑后续1">
    <condition type="exceed_day" value="857" />
    <condition type="should_finish" value="夷光4" />
    <condition type="should_not_finish" value="明月心低好感路线后续2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="玉玲珑17失败">
    <condition type="should_finish" value="玉玲珑15" />
    <condition type="should_not_finish" value="玉玲珑17失败" />
    <condition type="should_not_finish" value="玉玲珑支线1" />
    <condition type="should_not_finish" value="大唐1" />
    <condition type="exceed_day" value="930" />
  </trigger>
  <trigger story="灵鹫宫失败">
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫背叛1" />
    <condition type="should_not_finish" value="灵鹫宫背叛4后续" />
    <condition type="should_not_finish" value="灵鹫宫背叛7" />
    <condition type="exceed_day" value="910" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫背叛7">
    <condition type="exceed_day" value="893" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫背叛4后续" />
    <condition type="should_not_finish" value="灵鹫宫背叛7" />
    <condition type="should_not_finish" value="灵鹫宫失败" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫_攻打">
    <condition type="exceed_day" value="882" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="玉玲珑13" />
    <condition type="should_not_finish" value="灵鹫宫_委屈求全" />
    <condition type="should_not_finish" value="灵鹫宫_攻打" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="菊剑救援1">
    <condition type="exceed_day" value="894" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫_委屈求全" />
    <condition type="should_finish" value="李秋水询问2" />
    <condition type="should_not_finish" value="菊剑救援1" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="李秋水斜线1">
    <condition type="exceed_day" value="881" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫_委屈求全" />
    <condition type="should_not_finish" value="李秋水询问2" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="王语嫣回家">
    <condition type="exceed_day" value="920" />
    <condition type="in_team" value="王语嫣" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="天山童姥夺回灵鹫宫王语嫣" />
    <condition type="should_not_finish" value="灵鹫擂鼓山王语嫣" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="明月心高好感后续12">
    <condition type="should_finish" value="明月心高好感后续10" />
    <condition type="exceed_day" value="885" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月梅庄失败">
    <condition type="exceed_day" value="915" />
    <condition type="should_not_finish" value="梅庄救援8" />
    <condition type="should_finish" value="英雄会前夕事件" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月曲非烟武当">
    <condition type="exceed_day" value="905" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月盈盈善良线1">
    <condition type="exceed_day" value="930" />
    <condition type="should_finish" value="梅庄救援8" />
    <condition type="should_finish" value="拜师桑三娘" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="明月心高好感武当宴会">
    <condition type="exceed_day" value="915" />
    <condition type="should_finish" value="明月心高好感后续12" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月盈盈善良线2">
    <condition type="exceed_day" value="940" />
    <condition type="should_finish" value="日月盈盈善良线1" />
    <condition type="should_finish" value="拜师桑三娘" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月东方善良线2">
    <condition type="exceed_day" value="940" />
    <condition type="should_finish" value="日月东方善良线1" />
    <condition type="should_finish" value="拜师东方不败" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="宋青书寿宴前一个月">
    <condition type="exceed_day" value="905" />
    <condition type="in_team" value="宋青书" />
    <condition type="should_finish" value="宋青书纯爱开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="宋青书结局开启2">
    <condition type="exceed_day" value="935" />
    <condition type="in_team" value="宋青书" />
    <condition type="should_finish" value="宋青书结局开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="武当宴会丐帮">
    <condition type="exceed_day" value="918" />
    <condition type="in_menpai" value="丐帮" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="三阶段开始">
    <condition type="exceed_day" value="940" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="三阶段开始">
    <condition type="exceed_day" value="940" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="三阶段开始">
    <condition type="exceed_day" value="940" />
    <condition type="should_not_finish" value="明月心低好感夷光2" />
    <condition type="should_not_finish" value="东方不败死亡0" />
    <condition type="should_not_finish" value="明月心高好感后续12" />
    <condition type="should_not_finish" value="日月盈盈善良线1" />
    <condition type="in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="三阶段宴会DEBUG">
    <condition type="should_finish" value="三阶段欺骗事件" />
    <condition type="exceed_day" value="940" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="五岳大会预告">
    <condition type="should_finish" value="日月盈盈善良线6" />
    <condition type="exceed_day" value="960" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="三阶段上官仙欺骗4后续1">
    <condition type="not_has_time_key" value="上官仙离开" />
    <condition type="should_finish" value="三阶段上官仙欺骗4" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="东方不败出关">
    <condition type="exceed_day" value="980" />
    <condition type="should_finish" value="日月战嵩山7东方好感低2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="蒙古救援开始">
    <condition type="should_finish" value="九阴真经5主角郭靖" />
    <condition type="not_has_time_key" value="蒙古救援" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="快活林明月心提示">
    <condition type="should_not_finish" value="快活林4明月心战胜" />
    <condition type="not_in_team" value="李白" />
    <condition type="should_finish" value="成为日月教主明月心" />
    <condition type="exceed_day" value="977" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="初级任务全部完成">
    <condition type="should_finish" value="布娃娃不见了3" />
    <condition type="should_finish" value="小猫咪失踪了3" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="初级任务全部完成">
    <condition type="should_finish" value="正派布娃娃不见了3" />
    <condition type="should_finish" value="正派小猫咪失踪了3" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="中级任务全部完成">
    <condition type="should_finish" value="除掉情妇2" />
    <condition type="should_finish" value="家暴老公3" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="中级任务全部完成">
    <condition type="should_finish" value="正派除掉情妇4" />
    <condition type="should_finish" value="正派家暴老公3" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="霍青桐送信1">
    <condition type="should_finish" value="香香回部落1坚持2" />
    <condition type="not_has_time_key" value="霍青桐来信" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="霍青桐送信2">
    <condition type="should_finish" value="霍青桐送信1" />
    <condition type="not_has_time_key" value="霍青桐来信后" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="西湖回忆开始">
    <condition type="exceed_day" value="991" />
    <condition type="haogan_less_than" value="情人#1" />
    <condition type="should_finish" value="夷光事件厉凡" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="夷光10">
    <condition type="exceed_day" value="998" />
    <condition type="haogan_less_than" value="情人#2" />
    <condition type="should_finish" value="夷光10前置" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="笑傲五岳大会开始">
    <condition type="exceed_day" value="975" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="笑傲五岳大会开始">
    <condition type="exceed_day" value="975" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="笑傲五岳大会开始">
    <condition type="exceed_day" value="980" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="五毒教走向西域线" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="杨过神雕重阳宫大战1">
    <condition type="exceed_day" value="1020" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="主角杨过救小龙女2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="朋友神雕重阳宫大战1">
    <condition type="exceed_day" value="1020" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="小龙女走火入魔5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="龙女神雕重阳宫大战1">
    <condition type="exceed_day" value="1020" />
    <condition type="in_menpai" value="古墓" />
    <condition type="should_finish" value="主角走火入魔5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="西夏大战1">
    <condition type="exceed_day" value="1004" />
    <condition type="should_finish" value="灵鹫英雄会前夕李秋水" />
    <condition type="should_not_finish" value="李秋水斜线失败" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="灵鹫宫攻打1">
    <condition type="exceed_day" value="1025" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫背叛7" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="桑三娘死DEBUG">
    <condition type="should_finish" value="桑三娘死" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月岳不群攻山">
    <condition type="should_finish" value="桑三娘死" />
    <condition type="not_has_time_key" value="岳不群攻山" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="西夏大战2">
    <condition type="exceed_day" value="1050" />
    <condition type="should_finish" value="西夏大战1" />
    <condition type="should_not_finish" value="西夏大战2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡共通线">
    <condition type="exceed_day" value="1050" />
    <condition type="should_finish" value="明月心低好感夷光2" />
    <condition type="should_finish" value="笑傲五岳大会开始" />
    <condition type="should_not_finish" value="笑傲福州岳灵珊5" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡共通线五毒">
    <condition type="exceed_day" value="1050" />
    <condition type="should_finish" value="笑傲五岳大会开始" />
    <condition type="should_not_finish" value="笑傲福州岳灵珊5" />
    <condition type="in_menpai" value="五毒" />
    <condition type="should_not_finish" value="五毒教走向西域线" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡明月心">
    <condition type="exceed_day" value="1080" />
    <condition type="should_finish" value="成为日月教主明月心" />
    <condition type="should_not_finish" value="岳不群死亡明月心" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡共通线2">
    <condition type="exceed_day" value="1080" />
    <condition type="should_finish" value="岳不群死亡共通线" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡共通线2">
    <condition type="exceed_day" value="1080" />
    <condition type="should_finish" value="岳不群死亡共通线五毒" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月梅庄失败2">
    <condition type="exceed_day" value="1070" />
    <condition type="should_not_finish" value="决战黑木崖" />
    <condition type="should_finish" value="英雄会前夕事件" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="岳不群死亡">
    <condition type="should_finish" value="决战黑木崖胜利结束普通" />
    <condition type="exceed_day" value="1065" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期27">
    <condition type="exceed_day" value="889" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期28">
    <condition type="exceed_day" value="919" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期29">
    <condition type="exceed_day" value="949" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期30">
    <condition type="exceed_day" value="979" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期31">
    <condition type="exceed_day" value="1010" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期32">
    <condition type="exceed_day" value="1040" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期33">
    <condition type="exceed_day" value="1070" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="夷光16">
    <condition type="exceed_day" value="1065" />
    <condition type="should_not_finish" value="夷光16" />
    <condition type="should_finish" value="夷光14" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="中原武林大会预告">
    <condition type="exceed_day" value="1093" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="退出江湖" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期34">
    <condition type="exceed_day" value="1100" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="四阶段开始提醒">
    <condition type="exceed_day" value="1140" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期35">
    <condition type="exceed_day" value="1130" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期36">
    <condition type="exceed_day" value="1160" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期37">
    <condition type="exceed_day" value="1190" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期38">
    <condition type="exceed_day" value="1220" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期39">
    <condition type="exceed_day" value="1250" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期40">
    <condition type="exceed_day" value="1280" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期41">
    <condition type="exceed_day" value="1310" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="日月少林大战预告明月心">
    <condition type="should_finish" value="成为日月教主明月心" />
    <condition type="exceed_day" value="1270" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月少林大战预告">
    <condition type="should_finish" value="决战黑木崖胜利结束" />
    <condition type="exceed_day" value="1270" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月东方善良线8">
    <condition type="exceed_day" value="1270" />
    <condition type="should_finish" value="日月东方善良线2" />
    <condition type="not_in_team" value="任盈盈" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月东方善良线8任盈盈">
    <condition type="exceed_day" value="1270" />
    <condition type="should_finish" value="日月东方善良任盈盈线后续" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战灵鹫">
    <condition type="exceed_day" value="1250" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="灵鹫宫背叛7" />
    <condition type="should_not_finish" value="西夏大战6" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战灵鹫西夏">
    <condition type="exceed_day" value="1250" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="西夏大战6" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战灵鹫主线">
    <condition type="exceed_day" value="1250" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="灵鹫宫背叛7" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月明月心少林大战开始">
    <condition type="exceed_day" value="1300" />
    <condition type="should_finish" value="日月少林大战预告明月心" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月桑线少林大战开始">
    <condition type="exceed_day" value="1300" />
    <condition type="should_finish" value="日月少林大战预告" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="日月少林大战开始">
    <condition type="exceed_day" value="1300" />
    <condition type="should_not_finish" value="成为日月教主2" />
    <condition type="should_finish" value="日月东方善良线2" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战开始">
    <condition type="exceed_day" value="1270" />
    <condition type="should_not_finish" value="成为日月教主2" />
    <condition type="should_finish" value="明月心低好感路线后续" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战开始">
    <condition type="exceed_day" value="1270" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_in_menpai" value="灵鹫" />
    <condition type="should_not_finish" value="成为日月教主2" />
    <condition type="should_not_finish" value="少林大会主线开启" />
    <condition type="should_not_finish" value="少林大会特殊路线开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战灵鹫宫1">
    <condition type="exceed_day" value="1300" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="少林大会主线开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战西夏1">
    <condition type="exceed_day" value="1300" />
    <condition type="in_menpai" value="灵鹫" />
    <condition type="should_finish" value="西夏大战6" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战2">
    <condition type="exceed_day" value="1300" />
    <condition type="should_finish" value="明月心低好感路线后续" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段少林大战2">
    <condition type="exceed_day" value="1300" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="should_not_finish" value="少林大会主线开启" />
    <condition type="should_not_finish" value="少林大会特殊路线开启" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段岳不群2">
    <condition type="should_finish" value="四阶段岳不群" />
    <condition type="should_not_finish" value="四阶段裘千仞" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段宋远桥2">
    <condition type="should_finish" value="四阶段宋远桥" />
    <condition type="should_not_finish" value="四阶段丘处机" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="四阶段岳不群普通2">
    <condition type="should_finish" value="四阶段岳不群普通" />
    <condition type="should_not_finish" value="四阶段裘千仞" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="危险期42">
    <condition type="exceed_day" value="1340" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期43">
    <condition type="exceed_day" value="1370" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期44">
    <condition type="exceed_day" value="1400" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期45">
    <condition type="exceed_day" value="1430" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期46">
    <condition type="exceed_day" value="1460" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期47">
    <condition type="exceed_day" value="1490" />
    <condition type="should_not_finish" value="四阶段开始" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期48">
    <condition type="exceed_day" value="1520" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期49">
    <condition type="exceed_day" value="1550" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期50">
    <condition type="exceed_day" value="1580" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期51">
    <condition type="exceed_day" value="1610" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期52">
    <condition type="exceed_day" value="1640" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="危险期53">
    <condition type="exceed_day" value="1670" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="not_has_time_key" value="无法怀孕" />
  </trigger>
  <trigger story="剿灭门派完毕">
    <condition type="should_finish" value="剿灭丐帮成功" />
    <condition type="should_finish" value="剿灭全真成功" />
    <condition type="should_finish" value="剿灭白陀山庄" />
    <condition type="should_finish" value="剿灭武当成功" />
    <condition type="should_finish" value="剿灭少林成功" />
  </trigger>
  <trigger story="五阶段入宫">
    <condition type="exceed_day" value="1350" />
    <condition type="should_finish" value="少林大战结束2" />
    <condition type="should_not_finish" value="日月被围攻1" />
    <condition type="should_not_finish" value="日月少林大战结束黄裳" />
    <condition type="not_in_menpai" value="日月" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="五阶段入宫">
    <condition type="exceed_day" value="1350" />
    <condition type="should_finish" value="少林大战结束2" />
    <condition type="should_finish" value="明月心低好感夷光" />
    <condition type="should_not_finish" value="日月少林大战结束黄裳" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
    <condition type="should_not_finish" value="正常事件不能发生" />
  </trigger>
  <trigger story="小香事件7">
    <condition type="should_finish" value="五阶段入宫" />
    <condition type="exceed_day" value="1380" />
    <condition type="in_team" value="小香" />
    <condition type="haogan_more_than" value="小香#100" />
    <condition type="haogan_more_than" value="善良#50" />
    <condition type="should_finish" value="小香事件2百合后续战胜" />
    <condition type="should_not_finish" value="五阶段襄阳1" />
  </trigger>
  <trigger story="宋军营地孟珙日月">
    <condition type="should_finish" value="日月少林大战结束黄裳" />
    <condition type="should_not_finish" value="宋军营地孟珙日月" />
    <condition type="exceed_day" value="1370" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="日月五阶段襄阳1">
    <condition type="should_finish" value="日月实战训练" />
    <condition type="not_has_time_key" value="日月襄阳准备时间" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="宋军营地孟珙2">
    <condition type="should_not_finish" value="日月实战训练" />
    <condition type="exceed_day" value="1480" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="五阶段襄阳1">
    <condition type="exceed_day" value="1540" />
    <condition type="should_not_finish" value="日月实战训练" />
    <condition type="not_has_time_key" value="自动剧情不能进行" />
  </trigger>
  <trigger story="华山论剑开始DEBUG">
    <condition type="should_finish" value="华山论剑开始" />
  </trigger>
  <trigger story="门派古墓_拜师2DEBUG">
    <condition type="should_finish" value="门派古墓_拜师2" />
  </trigger>
  <trigger story="门派古墓_古墓出师DEBUG">
    <condition type="should_finish" value="门派古墓_古墓出师" />
  </trigger>
  <trigger story="华山论剑DEBUG">
    <condition type="should_finish" value="华山论剑欧阳锋2" />
  </trigger>
  <trigger story="华山论剑DEBUG">
    <condition type="should_finish" value="华山论剑欧阳锋1" />
  </trigger>
  <trigger story="绝情谷DEBUG">
    <condition type="should_finish" value="白飞飞1美" />
  </trigger>
  <trigger story="日月结局DEBUG">
    <condition type="should_finish" value="五阶段襄阳3日月专属" />
  </trigger>
  <trigger story="进入绿竹小巷">
    <condition type="should_finish" value="明月心高好感后续3" />
  </trigger>
  <trigger story="进入黑木崖">
    <condition type="should_finish" value="明月心高好感后续7" />
  </trigger>
  <trigger story="进入侠客岛">
    <condition type="should_finish" value="侠客行_侠客岛0" />
  </trigger>
  <trigger story="进入黑木崖">
    <condition type="should_finish" value="成为日月教主明月心" />
  </trigger>
  <trigger story="日月明月心回家地图">
    <condition type="should_finish" value="日月明月心回家石淇" />
    <condition type="should_finish" value="观音小筑开启" />
  </trigger>
  <trigger story="成为日月教主明月心DEBUG">
    <condition type="should_finish" value="成为日月教主明月心" />
  </trigger>
  <trigger story="成为日月教主DEBUG">
    <condition type="should_finish" value="成为日月教主" />
  </trigger>
  <trigger story="少林大会主线开启DEBUG">
    <condition type="should_finish" value="少林大会主线开启" />
  </trigger>
  <trigger story="少林大会特殊路线开启DEBUG">
    <condition type="should_finish" value="少林大会特殊路线开启" />
  </trigger>
  <trigger story="少林大会休息1DEBUG">
    <condition type="should_finish" value="少林大会休息1" />
  </trigger>
  <trigger story="少林大会休息2DEBUG">
    <condition type="should_finish" value="少林大会休息2" />
  </trigger>
  <trigger story="灵鹫宫背叛10DEBUG">
    <condition type="should_finish" value="灵鹫宫背叛10菊剑" />
  </trigger>
  <trigger story="灵鹫宫背叛10DEBUG">
    <condition type="should_finish" value="灵鹫宫背叛9符敏仪" />
  </trigger>
  <trigger story="少林大会休息DEBUG">
    <condition type="should_finish" value="四阶段少林大战2" />
  </trigger>
  <trigger story="少林大会灵鹫休息1DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫5符敏仪" />
  </trigger>
  <trigger story="少林大会灵鹫休息2DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫7符敏仪" />
  </trigger>
  <trigger story="少林大会灵鹫休息3DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫8符敏仪" />
  </trigger>
  <trigger story="少林大会灵鹫休息4DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫10符敏仪" />
  </trigger>
  <trigger story="少林大会灵鹫休息5DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫11符敏仪" />
  </trigger>
  <trigger story="少林大会灵鹫休息1DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫5菊剑" />
  </trigger>
  <trigger story="少林大会灵鹫休息2DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫7菊剑" />
  </trigger>
  <trigger story="少林大会灵鹫休息3DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫8菊剑" />
  </trigger>
  <trigger story="少林大会灵鹫休息4DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫10菊剑" />
  </trigger>
  <trigger story="少林大会灵鹫休息5DEBUG">
    <condition type="should_finish" value="四阶段少林大战灵鹫宫11菊剑" />
  </trigger>
  <trigger story="五毒教DEBUG">
    <condition type="in_menpai" value="五毒" />
  </trigger>
  <trigger story="成为五毒教主DEBUG">
    <condition type="should_finish" value="成为五毒教主1" />
  </trigger>
  <trigger story="一阶段五毒DEBUG">
    <condition type="should_finish" value="一阶段掌门人大会8五毒" />
  </trigger>
  <trigger story="五阶段襄阳4修正">
    <condition type="should_finish" value="五阶段襄阳4" />
  </trigger>
  <trigger story="女忍8自动">
    <condition type="should_finish" value="女忍8" />
  </trigger>
  <trigger story="浅雪堕落主角4修正">
    <condition type="should_finish" value="浅雪堕落主角4" />
  </trigger>
  <trigger story="霍青桐送信1DEBUG">
    <condition type="should_finish" value="霍青桐送信1" />
  </trigger>
  <trigger story="霍青桐送信2DEBUG">
    <condition type="should_finish" value="霍青桐送信2" />
  </trigger>
  <trigger story="明霞岛出来DEBUG">
    <condition type="should_finish" value="明霞岛出来" />
  </trigger>
  <trigger story="五毒教跳出DEBUG">
    <condition type="should_finish" value="拜师五毒教" />
  </trigger>
  <trigger story="越女剑结局DEBUG">
    <condition type="should_finish" value="越女剑结局判断" />
  </trigger>
</root>